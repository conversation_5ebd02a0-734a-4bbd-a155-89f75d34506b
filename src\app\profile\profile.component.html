<div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
  data-sidebar-position="fixed" data-header-position="fixed">
  
  <!-- Sidebar Start -->
  <aside class="left-sidebar">
    <!-- Sidebar scroll-->
    <div>
      <div class="login-header">
        <img src="assets/images/logos/esprit.png" alt="" style="width: 200px; height: auto; display: block; margin-left: 0px;" />
      </div>
      <div class="close-btn d-xl-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
        <i class="ti ti-x fs-8"></i>
      </div>
    </div>
    <!-- End Sidebar scroll-->
  </aside>
  <!--  Sidebar End -->

  <!--  Main wrapper -->
  <div class="body-wrapper">
    <!--  Header Start -->
    <header class="app-header">
      <nav class="navbar navbar-expand-lg navbar-light">
        <ul class="navbar-nav">
          <li class="nav-item d-block d-xl-none">
            <a class="nav-link sidebartoggler nav-icon-hover" id="headerCollapse" href="javascript:void(0)">
              <i class="ti ti-menu-2"></i>
            </a>
          </li>
        </ul>
        <div class="navbar-collapse justify-content-end px-0" id="navbarNav">
          <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-end">
            <li class="nav-item dropdown">
              <a class="nav-link nav-icon-hover" href="javascript:void(0)" id="drop2" data-bs-toggle="dropdown"
                aria-expanded="false">
                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                  <i class="ti ti-user text-white"></i>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop2">
                <div class="message-body">
                  <a href="javascript:void(0)" class="d-flex align-items-center gap-2 dropdown-item">
                    <i class="ti ti-user fs-6"></i>
                    <p class="mb-0 fs-3">{{currentUser?.username}}</p>
                  </a>
                  <a href="javascript:void(0)" (click)="goBack()" class="btn btn-outline-primary mx-3 mt-2 d-block">Retour au Dashboard</a>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nav>
    </header>
    <!--  Header End -->

    <div class="container-fluid">
      <!-- Profile Header -->
      <div class="welcome-header">
        <h1>Mon Profil</h1>
        <p>Gérez vos informations personnelles et professionnelles</p>
      </div>

      <!-- Success/Error Messages -->
      <div *ngIf="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="ti ti-check-circle me-2"></i>
        {{ successMessage }}
      </div>

      <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="ti ti-alert-circle me-2"></i>
        {{ errorMessage }}
      </div>

      <!-- Profile Card -->
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
          <div class="card">
            <div class="card-header bg-primary text-white">
              <h5 class="card-title mb-0">
                <i class="ti ti-user-circle me-2"></i>
                Informations du Profil
              </h5>
            </div>
            <div class="card-body">
              <!-- Loading Spinner -->
              <div *ngIf="isLoading" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Chargement...</span>
                </div>
              </div>

              <!-- Profile Form -->
              <form *ngIf="!isLoading" (ngSubmit)="saveProfile()" #profileFormRef="ngForm">
                <div class="row">
                  <!-- Username -->
                  <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">
                      <i class="ti ti-user me-1"></i>
                      Nom d'utilisateur
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="username"
                      name="username"
                      [(ngModel)]="profileData.username"
                      readonly
                      required>
                    <small class="text-muted">Le nom d'utilisateur ne peut pas être modifié</small>
                  </div>

                  <!-- Email -->
                  <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">
                      <i class="ti ti-mail me-1"></i>
                      Email
                    </label>
                    <input
                      type="email"
                      class="form-control"
                      id="email"
                      name="email"
                      [(ngModel)]="profileData.email"
                      readonly
                      required>
                    <small class="text-muted">L'email ne peut pas être modifié</small>
                  </div>

                  <!-- Role -->
                  <div class="col-md-6 mb-3">
                    <label for="role" class="form-label">
                      <i class="ti ti-shield me-1"></i>
                      Rôle
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="role"
                      [value]="currentUser?.role"
                      readonly>
                    <small class="text-muted">Le rôle ne peut pas être modifié</small>
                  </div>

                  <!-- Poste -->
                  <div class="col-md-6 mb-3">
                    <label for="poste" class="form-label">
                      <i class="ti ti-briefcase me-1"></i>
                      Poste
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="poste"
                      name="poste"
                      [(ngModel)]="profileData.poste"
                      readonly
                      required>
                    <small class="text-muted">Le poste ne peut pas être modifié</small>
                  </div>

                  <!-- Secteur -->
                  <div class="col-md-6 mb-3">
                    <label for="secteur" class="form-label">
                      <i class="ti ti-building me-1"></i>
                      Secteur
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="secteur"
                      name="secteur"
                      [(ngModel)]="profileData.secteur"
                      readonly
                      required>
                    <small class="text-muted">Le secteur ne peut pas être modifié</small>
                  </div>

                  <!-- Password Change Section -->
                  <div class="col-12 mb-3">
                    <div class="card border-primary">
                      <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                          <i class="ti ti-lock me-1"></i>
                          Changer le mot de passe
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="row">
                          <!-- Current Password -->
                          <div class="col-md-4 mb-3">
                            <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                            <input
                              type="password"
                              class="form-control"
                              id="currentPassword"
                              name="currentPassword"
                              [(ngModel)]="passwordData.currentPassword"
                              [disabled]="!isChangingPassword"
                              placeholder="Entrez votre mot de passe actuel">
                          </div>

                          <!-- New Password -->
                          <div class="col-md-4 mb-3">
                            <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                            <input
                              type="password"
                              class="form-control"
                              id="newPassword"
                              name="newPassword"
                              [(ngModel)]="passwordData.newPassword"
                              [disabled]="!isChangingPassword"
                              placeholder="Entrez le nouveau mot de passe">
                          </div>

                          <!-- Confirm Password -->
                          <div class="col-md-4 mb-3">
                            <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                            <input
                              type="password"
                              class="form-control"
                              id="confirmPassword"
                              name="confirmPassword"
                              [(ngModel)]="passwordData.confirmPassword"
                              [disabled]="!isChangingPassword"
                              placeholder="Confirmez le nouveau mot de passe">
                          </div>
                        </div>

                        <!-- Password validation messages -->
                        <div *ngIf="passwordData.newPassword && passwordData.confirmPassword && passwordData.newPassword !== passwordData.confirmPassword"
                             class="alert alert-danger small">
                          Les mots de passe ne correspondent pas.
                        </div>

                        <!-- Password change buttons -->
                        <div class="d-flex gap-2">
                          <button
                            type="button"
                            class="btn btn-outline-info btn-sm"
                            (click)="testBackend()">
                            <i class="ti ti-server me-1"></i>
                            Test Backend
                          </button>

                          <button
                            *ngIf="!isChangingPassword"
                            type="button"
                            class="btn btn-outline-primary btn-sm"
                            (click)="togglePasswordChange()">
                            <i class="ti ti-edit me-1"></i>
                            Modifier le mot de passe
                          </button>

                          <button
                            type="button"
                            class="btn btn-outline-warning btn-sm"
                            (click)="usePasswordReset()">
                            <i class="ti ti-mail me-1"></i>
                            Réinitialiser par email
                          </button>

                          <button
                            *ngIf="isChangingPassword"
                            type="button"
                            class="btn btn-secondary btn-sm"
                            (click)="cancelPasswordChange()">
                            <i class="ti ti-x me-1"></i>
                            Annuler
                          </button>

                          <button
                            *ngIf="isChangingPassword"
                            type="button"
                            class="btn btn-success btn-sm"
                            [disabled]="!isPasswordValid() || isLoading"
                            (click)="changePassword()">
                            <i class="ti ti-check me-1"></i>
                            <span *ngIf="!isLoading">Changer le mot de passe</span>
                            <span *ngIf="isLoading">Changement...</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-start mt-4">
                  <button type="button" class="btn btn-secondary" (click)="goBack()">
                    <i class="ti ti-arrow-left me-1"></i>
                    Retour
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
