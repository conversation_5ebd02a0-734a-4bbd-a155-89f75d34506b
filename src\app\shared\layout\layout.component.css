/* Enhanced Layout Styles */
.page-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%);
  position: relative;
}

.page-wrapper::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(108, 117, 125, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.body-wrapper {
  margin-left: 280px;
  min-height: 100vh;
  transition: all 0.3s ease;
}

.main-content {
  padding-top: 100px; /* Account for fixed navbar height */
  padding-bottom: 2rem;
  padding-left: 2rem;
  padding-right: 2rem;
  min-height: calc(100vh - 100px);
}

/* Mobile Responsiveness */
@media (max-width: 1199.98px) {
  .body-wrapper {
    margin-left: 0;
  }
}

/* Sidebar collapsed state */
.sidebar-collapsed .body-wrapper {
  margin-left: 80px;
}

@media (max-width: 1199.98px) {
  .sidebar-collapsed .body-wrapper {
    margin-left: 0;
  }
}

/* Content animations */
.main-content {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    padding: 90px 1rem 2rem;
  }
}

/* Loading overlay (can be used for future loading states) */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.3rem solid #f3f3f3;
  border-top: 0.3rem solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
