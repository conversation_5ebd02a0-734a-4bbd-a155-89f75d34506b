/* Layout Styles */
.page-wrapper {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.body-wrapper {
  margin-left: 270px;
  min-height: 100vh;
  transition: margin-left 0.3s ease;
}

.main-content {
  padding-top: 90px; /* Account for fixed navbar height */
  padding-bottom: 2rem;
  min-height: calc(100vh - 90px);
}

/* Mobile Responsiveness */
@media (max-width: 1199.98px) {
  .body-wrapper {
    margin-left: 0;
  }
}

/* Sidebar collapsed state */
.sidebar-collapsed .body-wrapper {
  margin-left: 80px;
}

@media (max-width: 1199.98px) {
  .sidebar-collapsed .body-wrapper {
    margin-left: 0;
  }
}

/* Content animations */
.main-content {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    padding: 90px 1rem 2rem;
  }
}

/* Loading overlay (can be used for future loading states) */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.3rem solid #f3f3f3;
  border-top: 0.3rem solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
