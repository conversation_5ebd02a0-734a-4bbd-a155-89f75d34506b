/* Navbar Styles */
.app-header {
  position: fixed;
  top: 0;
  right: 0;
  left: 270px; /* Adjust based on sidebar width */
  z-index: 1030;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease;
}

.navbar {
  padding: 1rem 1.5rem;
  min-height: 70px;
}

.nav-icon-hover {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.nav-icon-hover:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.notification {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  min-width: 200px;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.dropdown-divider {
  margin: 0.5rem 0;
}

/* Mobile responsiveness */
@media (max-width: 1199.98px) {
  .app-header {
    left: 0;
  }
}

/* Sidebar collapsed state */
.sidebar-collapsed .app-header {
  left: 80px;
}

@media (max-width: 1199.98px) {
  .sidebar-collapsed .app-header {
    left: 0;
  }
}
