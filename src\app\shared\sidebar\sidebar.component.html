<aside class="left-sidebar">
  <!-- Sidebar scroll-->
  <div>
    <!-- <PERSON><PERSON> and Brand -->
    <div class="brand-logo d-flex align-items-center justify-content-between">
      <div class="login-header">
        <img src="assets/images/logos/esprit.png" alt="ESPRIT Logo" class="sidebar-logo" />
      </div>
      <div class="close-btn d-xl-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse" (click)="toggleSidebar()">
        <i class="ti ti-x fs-8"></i>
      </div>
    </div>
    
    <!-- User Info Section -->
    <div class="user-info-section" *ngIf="currentUser">
      <div class="user-avatar">
        <img src="assets/images/profile/user-1.jpg" alt="User Avatar" class="rounded-circle">
      </div>
      <div class="user-details">
        <h6 class="user-name">{{ currentUser.username }}</h6>
        <p class="user-role">{{ getRoleDisplayName(currentUser.role) }}</p>
      </div>
    </div>
    
    <!-- Sidebar navigation-->
    <nav class="sidebar-nav scroll-sidebar" data-simplebar="">
      <ul id="sidebarnav">
        <li class="nav-small-cap">
          <span class="hide-menu">Navigation</span>
        </li>

        <!-- Dynamic Navigation Items -->
        <li class="sidebar-item" *ngFor="let item of navigationItems">
          <a class="sidebar-link" 
             [class.active]="isActiveRoute(item.route || '')"
             (click)="item.route ? navigateTo(item.route) : executeAction(item.action)"
             href="javascript:void(0)" 
             aria-expanded="false">
            <i [class]="item.icon"></i>
            <span class="hide-menu">{{ item.title }}</span>
          </a>
        </li>

        <!-- Separator -->
        <li class="nav-small-cap">
          <span class="hide-menu">Compte</span>
        </li>

        <!-- Profile Link -->
        <li class="sidebar-item">
          <a class="sidebar-link" 
             [class.active]="isActiveRoute('/profile')"
             (click)="navigateToProfile()" 
             href="javascript:void(0)" 
             aria-expanded="false">
            <i class="ti ti-user"></i>
            <span class="hide-menu">Mon Profil</span>
          </a>
        </li>

        <!-- Logout Link -->
        <li class="sidebar-item">
          <a class="sidebar-link" 
             (click)="logout()" 
             href="javascript:void(0)" 
             aria-expanded="false">
            <i class="ti ti-logout"></i>
            <span class="hide-menu">Déconnexion</span>
          </a>
        </li>
      </ul>
    </nav>
    <!-- End Sidebar navigation -->
  </div>
  <!-- End Sidebar scroll-->
</aside>
