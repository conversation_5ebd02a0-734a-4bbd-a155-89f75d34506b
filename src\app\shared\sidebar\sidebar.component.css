/* Sidebar Styles */
.left-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 270px;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1040;
  transition: all 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.brand-logo {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  width: 180px;
  height: auto;
  filter: brightness(0) invert(1);
}

/* User Info Section */
.user-info-section {
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
}

.user-avatar img {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 1rem;
}

.user-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: white;
}

.user-role {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-bottom: 0;
  color: rgba(255, 255, 255, 0.8);
}

/* Navigation Styles */
.sidebar-nav {
  padding: 1rem 0;
}

.nav-small-cap {
  padding: 1rem 1.5rem 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.nav-small-cap .hide-menu {
  color: rgba(255, 255, 255, 0.6);
}

.sidebar-item {
  margin: 0.25rem 0.75rem;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 10px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.sidebar-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(5px);
}

.sidebar-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sidebar-link i {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 24px;
  text-align: center;
}

.hide-menu {
  font-size: 0.9rem;
}

/* Close button */
.close-btn {
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Collapsed Sidebar */
.left-sidebar.collapsed {
  width: 80px;
}

.left-sidebar.collapsed .hide-menu,
.left-sidebar.collapsed .nav-small-cap,
.left-sidebar.collapsed .user-details,
.left-sidebar.collapsed .sidebar-logo {
  display: none;
}

.left-sidebar.collapsed .user-info-section {
  padding: 1rem 0.5rem;
}

.left-sidebar.collapsed .sidebar-link {
  justify-content: center;
  padding: 0.875rem 0.5rem;
}

.left-sidebar.collapsed .sidebar-link i {
  margin-right: 0;
}

/* Mobile Responsiveness */
@media (max-width: 1199.98px) {
  .left-sidebar {
    transform: translateX(-100%);
  }
  
  .left-sidebar.show {
    transform: translateX(0);
  }
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animation for menu items */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-item {
  animation: slideInLeft 0.3s ease-out;
}

.sidebar-item:nth-child(1) { animation-delay: 0.1s; }
.sidebar-item:nth-child(2) { animation-delay: 0.2s; }
.sidebar-item:nth-child(3) { animation-delay: 0.3s; }
.sidebar-item:nth-child(4) { animation-delay: 0.4s; }
.sidebar-item:nth-child(5) { animation-delay: 0.5s; }
