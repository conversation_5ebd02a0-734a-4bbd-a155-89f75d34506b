package com.example.microserviceuser.Controller;

import com.example.microserviceuser.Entity.User;
import com.example.microserviceuser.Repository.UserRepository;
import com.example.microserviceuser.dto.PasswordChangeRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "http://localhost:4200")
public class UserController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // Test endpoint to verify backend is running
    @GetMapping("/test")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Backend is running successfully!");
    }

    // Endpoint accessible seulement par les chefs de département
    @GetMapping("/all")
    @PreAuthorize("hasRole('CHEF DEPARTEMENT')")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userRepository.findAll();
        return ResponseEntity.ok(users);
    }

    // Endpoint accessible par tous les utilisateurs authentifiés
    @GetMapping("/profile")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<User> getUserProfile(@RequestParam String email) {
        User user = userRepository.findByEmail(email);
        if (user != null) {
            return ResponseEntity.ok(user);
        }
        return ResponseEntity.notFound().build();
    }

    // Endpoint pour mettre à jour le profil (accessible par tous les utilisateurs authentifiés)
    @PutMapping("/profile")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<User> updateProfile(@RequestBody User user) {
        User existingUser = userRepository.findByEmail(user.getEmail());
        if (existingUser != null) {
            existingUser.setUsername(user.getUsername());
            existingUser.setPoste(user.getPoste());
            existingUser.setSecteur(user.getSecteur());
            userRepository.save(existingUser);
            return ResponseEntity.ok(existingUser);
        }
        return ResponseEntity.notFound().build();
    }

    // Endpoint pour changer le mot de passe (accessible par tous les utilisateurs authentifiés)
    @PutMapping("/change-password")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, String>> changePassword(@RequestBody PasswordChangeRequest request, Authentication authentication) {
        System.out.println("Change password request received for user: " + authentication.getName());
        System.out.println("Request data - Current password provided: " + (request.getCurrentPassword() != null && !request.getCurrentPassword().isEmpty()));
        System.out.println("Request data - New password provided: " + (request.getNewPassword() != null && !request.getNewPassword().isEmpty()));

        Map<String, String> response = new HashMap<>();
        String userEmail = authentication.getName();
        User existingUser = userRepository.findByEmail(userEmail);

        if (existingUser == null) {
            System.out.println("User not found: " + userEmail);
            response.put("error", "Utilisateur non trouvé");
            return ResponseEntity.status(404).body(response);
        }

        // Vérification mot de passe actuel
        System.out.println("Verifying current password...");
        if (!passwordEncoder.matches(request.getCurrentPassword(), existingUser.getPassword())) {
            System.out.println("Current password verification failed");
            response.put("error", "Mot de passe actuel incorrect");
            return ResponseEntity.badRequest().body(response);
        }

        // Vérification nouveau mot de passe
        if (request.getNewPassword() == null || request.getNewPassword().length() < 6) {
            System.out.println("New password validation failed - too short or null");
            response.put("error", "Le nouveau mot de passe doit contenir au moins 6 caractères");
            return ResponseEntity.badRequest().body(response);
        }

        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            System.out.println("Password confirmation mismatch");
            response.put("error", "Les mots de passe ne correspondent pas");
            return ResponseEntity.badRequest().body(response);
        }

        // Mise à jour mot de passe
        System.out.println("Updating password...");
        existingUser.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(existingUser);
        System.out.println("Password updated successfully");

        response.put("message", "Mot de passe changé avec succès");
        return ResponseEntity.ok(response);
    }


    // Endpoint pour supprimer un utilisateur (seulement chef de département)
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CHEF DEPARTEMENT')")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        if (userRepository.existsById(id)) {
            userRepository.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Endpoint pour obtenir les utilisateurs par rôle (seulement chef de département)
    @GetMapping("/by-role/{role}")
    @PreAuthorize("hasRole('CHEF DEPARTEMENT')")
    public ResponseEntity<List<User>> getUsersByRole(@PathVariable String role) {
        List<User> users = userRepository.findByRole(role);
        return ResponseEntity.ok(users);
    }

    // Endpoint pour trouver le chef de département par secteur
    @GetMapping("/chef-by-sector/{sector}")
    public ResponseEntity<String> getChefBySector(@PathVariable String sector) {
        User chef = userRepository.findByRoleAndSecteur("chef departement", sector);
        if (chef != null) {
            return ResponseEntity.ok(chef.getEmail());
        }
        // Return default chef if no specific chef found for the sector
        return ResponseEntity.ok("<EMAIL>");
    }
}
