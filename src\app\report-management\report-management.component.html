<app-layout>
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Gestion des Rapports</h2>
        <button type="button" class="btn btn-primary" (click)="openCreateModal()">
          <i class="fas fa-plus"></i> Nouveau Rapport
        </button>
      </div>
      
      <!-- Success/Error Messages -->
      <div *ngIf="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
        {{ successMessage }}
        <button type="button" class="btn-close" (click)="successMessage = ''" aria-label="Close"></button>
      </div>
      
      <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ errorMessage }}
        <button type="button" class="btn-close" (click)="errorMessage = ''" aria-label="Close"></button>
      </div>

      <!-- View Tabs -->
      <ul class="nav nav-tabs mb-4">
        <li class="nav-item">
          <a class="nav-link" 
             [class.active]="currentView === 'all'" 
             (click)="currentView = 'all'">
            Tous les Rapports
            <span class="badge bg-secondary ms-2">{{ myReports.length }}</span>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" 
             [class.active]="currentView === 'drafts'" 
             (click)="currentView = 'drafts'">
            Brouillons
            <span class="badge bg-warning ms-2">{{ drafts.length }}</span>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" 
             [class.active]="currentView === 'validated'" 
             (click)="currentView = 'validated'">
            Validés
            <span class="badge bg-success ms-2">{{ validatedReports.length }}</span>
          </a>
        </li>
      </ul>

      <!-- Reports List -->
      <div class="card">
        <div class="card-body">
          <div *ngIf="getCurrentReports().length === 0" class="text-muted text-center py-5">
            <i class="fas fa-file-alt fa-3x mb-3"></i>
            <p>Aucun rapport trouvé.</p>
          </div>
          
          <div *ngIf="getCurrentReports().length > 0" class="row">
            <div class="col-md-6 col-lg-4 mb-4" *ngFor="let report of getCurrentReports()">
              <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <span class="badge" [ngClass]="getStatusBadgeClass(report.statut)">
                    {{ getStatusText(report.statut) }}
                  </span>
                  <small class="text-muted">{{ report.dateCreation | date:'short' }}</small>
                </div>
                <div class="card-body">
                  <h6 class="card-title">{{ report.titre }}</h6>
                  <p class="card-text text-muted small">
                    <strong>Classe:</strong> {{ report.classe }}<br>
                    <strong>Secteur:</strong> {{ report.secteur }}<br>
                    <strong>Semestre:</strong> {{ report.semestre }}
                  </p>
                  <p class="card-text">
                    {{ report.contenu.length > 100 ? (report.contenu | slice:0:100) + '...' : report.contenu }}
                  </p>
                </div>
                <div class="card-footer">
                  <div class="btn-group w-100" role="group">
                    <button
                      type="button"
                      class="btn btn-outline-primary btn-sm"
                      (click)="openViewModal(report)"
                      title="Voir le rapport">
                      <i class="fas fa-eye"></i>
                      <span class="d-none d-md-inline ms-1">Voir</span>
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary btn-sm"
                      (click)="openEditModal(report)"
                      [disabled]="!canEdit(report)"
                      title="Modifier le rapport">
                      <i class="fas fa-edit"></i>
                      <span class="d-none d-md-inline ms-1">Modifier</span>
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-success btn-sm"
                      (click)="onValidate(report)"
                      [disabled]="!canValidate(report) || isValidating"
                      title="Valider le rapport">
                      <i class="fas fa-check"></i>
                      <span class="d-none d-md-inline ms-1">Valider</span>
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm"
                      (click)="openDeleteModal(report)"
                      [disabled]="!canDelete(report)"
                      title="Supprimer le rapport">
                      <i class="fas fa-trash"></i>
                      <span class="d-none d-md-inline ms-1">Supprimer</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

<!-- Create Report Modal -->
<div class="modal" [class.show]="showCreateModal" [style.display]="showCreateModal ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Nouveau Rapport</h5>
        <button type="button" class="btn-close" (click)="closeCreateModal()"></button>
      </div>
      <div class="modal-body">
        <form (ngSubmit)="onSubmit()" #createForm="ngForm">
          <div class="row">
  <!-- Titre -->
  <div class="col-md-8">
    <div class="mb-3">
      <label for="titre" class="form-label">Titre *</label>
      <input 
        type="text" 
        class="form-control" 
        id="titre" 
        [(ngModel)]="formData.titre" 
        name="titre" 
        required>
    </div>
  </div>

  <div class="row">
  <div class="col-md-4">
    <div class="mb-3">
      <label for="secteur" class="form-label">Secteur *</label>
      <select class="form-select" id="secteur" [(ngModel)]="formData.secteur" name="secteur" (change)="onSecteurChange()" required>
        <option value="">Sélectionner</option>
        <option *ngFor="let secteur of secteurs" [value]="secteur">{{ secteur }}</option>
      </select>
    </div>
  </div>

  <div class="col-md-4" *ngIf="options.length > 0">
    <div class="mb-3">
      <label for="option" class="form-label">Option *</label>
      <select class="form-select" id="option" [(ngModel)]="formData.option" name="option" (change)="onOptionChange()" required>
        <option value="">Sélectionner une option</option>
        <option *ngFor="let opt of options" [value]="opt">{{ opt }}</option>
      </select>
    </div>
  </div>

  <div class="col-md-4" *ngIf="classes.length > 0">
    <div class="mb-3">
      <label for="classe" class="form-label">Classe *</label>
      <select class="form-select" id="classe" [(ngModel)]="formData.classe" name="classe" required>
        <option value="">Sélectionner une classe</option>
        <option *ngFor="let classe of classes" [value]="classe">{{ classe }}</option>
      </select>
    </div>
  </div>
</div>

            <div class="col-md-4">
              <div class="mb-3">
                <label for="anneeAcademique" class="form-label">Année Académique *</label>
                <input 
                  type="text" 
                  class="form-control" 
                  id="anneeAcademique" 
                  [(ngModel)]="formData.anneeAcademique" 
                  name="anneeAcademique" 
                  placeholder="2024-2025"
                  required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="semestre" class="form-label">Semestre *</label>
                <select 
                  class="form-select" 
                  id="semestre" 
                  [(ngModel)]="formData.semestre" 
                  name="semestre" 
                  required>
                  <option value="">Sélectionner</option>
                  <option *ngFor="let semestre of semestres" [value]="semestre">{{ semestre }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="contenu" class="form-label">Contenu *</label>
            <textarea 
              class="form-control" 
              id="contenu" 
              [(ngModel)]="formData.contenu" 
              name="contenu" 
              rows="8" 
              required></textarea>
          </div>

          <div *ngIf="errorMessage" class="alert alert-danger">
            {{ errorMessage }}
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
          Annuler
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="onSubmit()"
          [disabled]="isSubmitting">
          <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isSubmitting ? 'Création...' : 'Créer' }}
        </button>
      </div>
    </div>
  </div>
</div>
<div *ngIf="showCreateModal" class="modal-backdrop fade show"></div>

<!-- Edit Report Modal -->
<div class="modal" [class.show]="showEditModal" [style.display]="showEditModal ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Modifier le Rapport</h5>
        <button type="button" class="btn-close" (click)="closeEditModal()"></button>
      </div>
      <div class="modal-body" *ngIf="selectedReport">
        <form (ngSubmit)="onUpdate()" #editForm="ngForm">
          <div class="row">
            <div class="col-md-8">
              <div class="mb-3">
                <label for="editTitre" class="form-label">Titre *</label>
                <input
                  type="text"
                  class="form-control"
                  id="editTitre"
                  [(ngModel)]="editFormData.titre"
                  name="editTitre"
                  required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="editClasse" class="form-label">Classe *</label>
                <input
                  type="text"
                  class="form-control"
                  id="editClasse"
                  [(ngModel)]="editFormData.classe"
                  name="editClasse"
                  required>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="editSecteur" class="form-label">Secteur *</label>
                <select
                  class="form-select"
                  id="editSecteur"
                  [(ngModel)]="editFormData.secteur"
                  name="editSecteur"
                  required>
                  <option value="">Sélectionner</option>
                  <option *ngFor="let secteur of secteurs" [value]="secteur">{{ secteur }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="editAnneeAcademique" class="form-label">Année Académique *</label>
                <input
                  type="text"
                  class="form-control"
                  id="editAnneeAcademique"
                  [(ngModel)]="editFormData.anneeAcademique"
                  name="editAnneeAcademique"
                  required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="editSemestre" class="form-label">Semestre *</label>
                <select
                  class="form-select"
                  id="editSemestre"
                  [(ngModel)]="editFormData.semestre"
                  name="editSemestre"
                  required>
                  <option value="">Sélectionner</option>
                  <option *ngFor="let semestre of semestres" [value]="semestre">{{ semestre }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="editContenu" class="form-label">Contenu *</label>
            <textarea
              class="form-control"
              id="editContenu"
              [(ngModel)]="editFormData.contenu"
              name="editContenu"
              rows="8"
              required></textarea>
          </div>

          <div *ngIf="errorMessage" class="alert alert-danger">
            {{ errorMessage }}
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
          Annuler
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="onUpdate()"
          [disabled]="isUpdating">
          <span *ngIf="isUpdating" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isUpdating ? 'Mise à jour...' : 'Mettre à jour' }}
        </button>
      </div>
    </div>
  </div>
</div>
<div *ngIf="showEditModal" class="modal-backdrop fade show"></div>

<!-- View Report Modal -->
<div class="modal" [class.show]="showViewModal" [style.display]="showViewModal ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ selectedReport?.titre }}</h5>
        <button type="button" class="btn-close" (click)="closeViewModal()"></button>
      </div>
      <div class="modal-body" *ngIf="selectedReport">
        <div class="row mb-3">
          <div class="col-md-6">
            <strong>Classe:</strong> {{ selectedReport.classe }}
          </div>
          <div class="col-md-6">
            <strong>Secteur:</strong> {{ selectedReport.secteur }}
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <strong>Année Académique:</strong> {{ selectedReport.anneeAcademique }}
          </div>
          <div class="col-md-6">
            <strong>Semestre:</strong> {{ selectedReport.semestre }}
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <strong>Statut:</strong>
            <span class="badge ms-2" [ngClass]="getStatusBadgeClass(selectedReport.statut)">
              {{ getStatusText(selectedReport.statut) }}
            </span>
          </div>
          <div class="col-md-6">
            <strong>Date de création:</strong> {{ selectedReport.dateCreation | date:'short' }}
          </div>
        </div>
        <div *ngIf="selectedReport.dateModification" class="row mb-3">
          <div class="col-md-6">
            <strong>Dernière modification:</strong> {{ selectedReport.dateModification | date:'short' }}
          </div>
          <div class="col-md-6" *ngIf="selectedReport.dateValidation">
            <strong>Date de validation:</strong> {{ selectedReport.dateValidation | date:'short' }}
          </div>
        </div>
        <hr>
        <div class="mb-3">
          <strong>Contenu:</strong>
        </div>
        <div class="border rounded p-3 bg-light" style="white-space: pre-wrap;">{{ selectedReport.contenu }}</div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeViewModal()">
          Fermer
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="openEditModal(selectedReport!); closeViewModal()"
          [disabled]="!canEdit(selectedReport!)">
          <i class="fas fa-edit"></i> Modifier
        </button>
      </div>
    </div>
  </div>
</div>
<div *ngIf="showViewModal" class="modal-backdrop fade show"></div>

<!-- Delete Confirmation Modal -->
<div class="modal" [class.show]="showDeleteModal" [style.display]="showDeleteModal ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Confirmer la suppression</h5>
        <button type="button" class="btn-close" (click)="closeDeleteModal()"></button>
      </div>
      <div class="modal-body" *ngIf="selectedReport">
        <p>Êtes-vous sûr de vouloir supprimer le rapport <strong>"{{ selectedReport.titre }}"</strong> ?</p>
        <p class="text-danger">Cette action est irréversible.</p>

        <div *ngIf="errorMessage" class="alert alert-danger">
          {{ errorMessage }}
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeDeleteModal()">
          Annuler
        </button>
        <button
          type="button"
          class="btn btn-danger"
          (click)="onDelete()"
          [disabled]="isDeleting">
          <span *ngIf="isDeleting" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isDeleting ? 'Suppression...' : 'Supprimer' }}
        </button>
      </div>
    </div>
  </div>
</div>
<div *ngIf="showDeleteModal" class="modal-backdrop fade show"></div>

</app-layout>
